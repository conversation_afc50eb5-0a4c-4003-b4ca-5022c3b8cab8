# Development Environment Configuration
# IMPORTANT: This file is for LOCAL DEVELOPMENT with hot reload support

# BGE Server Configuration
# The BGE embedding server runs on the Ubuntu server at 192.168.1.84
BGE_SERVER_URL=http://192.168.1.84:8080

# LLM Configuration (copy from your main .env)
LLM_PROVIDER=openrouter
LLM_API_KEY=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
LLM_CHOICE=google/gemini-2.5-flash-lite
LLM_BASE_URL=https://openrouter.ai/api/v1

# Database Configuration
# For development, we use local PostgreSQL in Docker
# For production, this would point to Supabase
DATABASE_URL=******************************************************/spark_memory

# External Supabase (if needed for testing with real data)
SUPABASE_URL=http://*************:8000
SUPABASE_DATABASE_URL=**************************************************************************/postgres

# Neo4j Configuration
# Neo4j connection URI - use bolt://localhost:7687
# IMPORTANT: If running the MCP server through Docker, change localhost to host.docker.internal
NEO4J_URI=bolt://localhost:7687

# Neo4j username (usually 'neo4j' for default installations)
NEO4J_USER=neo4j

# Neo4j password for your database instance
NEO4J_PASSWORD=Ahayh@5096$

# Development Settings
ENVIRONMENT=development
LOG_LEVEL=DEBUG
PYTHONUNBUFFERED=1

# MCP Settings
TRANSPORT=sse
HOST=0.0.0.0
PORT=8050

# Optional: User ID for testing
MCP_USER_ID=dev_user

# Enhanced BGE Configuration for Testing
USE_DIRECT_EMBEDDING=true
EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5
EMBEDDING_BATCH_SIZE=32
EMBEDDING_DEVICE=cuda
BGE_TIMEOUT=30
